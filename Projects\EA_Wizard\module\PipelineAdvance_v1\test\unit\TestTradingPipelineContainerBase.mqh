//+------------------------------------------------------------------+
//|                                TestTradingPipelineContainerBase.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerBase.mqh"
#include "../integration/MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| TradingPipelineContainerBase 單元測試類                         |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerBase : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineContainerBase(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineContainerBase"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineContainerBase() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineContainerBase 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestProtectedMethods();
        TestTemplateMethodPattern();
        TestPipelineManagement();
        TestExecuteFlow();

        Print("=== TradingPipelineContainerBase 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineContainerBase 構造函數 ---");

        // 測試基本構造函數
        TestTradingPipelineContainerImpl* container1 = new TestTradingPipelineContainerImpl("BaseTest");
        
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestConstructor - 基本構造",
                container1 != NULL && container1.GetName() == "BaseTest",
                "基本構造函數正常"
            ));
        }

        // 測試帶參數構造函數
        TestTradingPipelineContainerImpl* container2 = new TestTradingPipelineContainerImpl(
            "ParamTest", "測試描述", "TestType", false, 10);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestConstructor - 帶參數構造",
                container2 != NULL && 
                container2.GetName() == "ParamTest" &&
                container2.GetDescription() == "測試描述" &&
                container2.GetType() == "TestType" &&
                container2.GetMaxPipelines() == 10,
                "帶參數構造函數正常"
            ));
        }

        delete container1;
        delete container2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineContainerBase 基本屬性 ---");

        TestTradingPipelineContainerImpl* container = new TestTradingPipelineContainerImpl(
            "PropTest", "屬性測試", "TestType", false, 5);

        // 測試基本屬性
        string name = container.GetName();
        string type = container.GetType();
        string desc = container.GetDescription();
        int maxPipelines = container.GetMaxPipelines();
        bool executed = container.IsExecuted();
        bool enabled = container.IsEnabled();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestBasicProperties - 屬性獲取",
                name == "PropTest" && type == "TestType" && desc == "屬性測試" &&
                maxPipelines == 5 && !executed && enabled,
                "所有基本屬性正確"
            ));
        }

        // 測試狀態方法
        bool isEmpty = container.IsEmpty();
        bool isFull = container.IsFull();
        int count = container.GetPipelineCount();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestBasicProperties - 狀態方法",
                isEmpty && !isFull && count == 0,
                "初始狀態方法正確"
            ));
        }

        delete container;
    }

    // 測試受保護方法
    void TestProtectedMethods()
    {
        Print("--- 測試 TradingPipelineContainerBase 受保護方法 ---");

        TestTradingPipelineContainerImpl* container = new TestTradingPipelineContainerImpl("ProtectedTest");

        // 測試 SetResult 方法（通過公共方法間接測試）
        container.TestSetResult(true, "測試成功", ERROR_LEVEL_INFO);
        PipelineResult* result = container.GetResult();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestProtectedMethods - SetResult",
                result != NULL && result.IsSuccess() && result.GetMessage() == "測試成功",
                "SetResult 方法正常工作"
            ));
        }

        // 測試模板方法的調用順序
        container.TestTemplateMethodCalls();
        bool callOrderCorrect = container.GetPreExecuteCallCount() > 0 &&
                               container.GetExecuteInternalCallCount() > 0 &&
                               container.GetPostExecuteCallCount() > 0;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestProtectedMethods - 模板方法調用",
                callOrderCorrect,
                "模板方法調用順序正確"
            ));
        }

        delete container;
    }

    // 測試模板方法模式
    void TestTemplateMethodPattern()
    {
        Print("--- 測試 TradingPipelineContainerBase 模板方法模式 ---");

        TestTradingPipelineContainerImpl* container = new TestTradingPipelineContainerImpl("TemplateTest");

        // 重置計數器
        container.ResetCallCounts();

        // 執行容器（觸發模板方法）
        container.Execute();

        // 檢查模板方法的執行順序
        bool preExecuteCalled = container.GetPreExecuteCallCount() == 1;
        bool executeInternalCalled = container.GetExecuteInternalCallCount() == 1;
        bool postExecuteCalled = container.GetPostExecuteCallCount() == 1;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestTemplateMethodPattern - 執行順序",
                preExecuteCalled && executeInternalCalled && postExecuteCalled,
                "模板方法執行順序正確"
            ));
        }

        // 測試重複執行防護
        container.Execute();
        bool noRepeatedExecution = container.GetPreExecuteCallCount() == 1 &&
                                  container.GetExecuteInternalCallCount() == 1 &&
                                  container.GetPostExecuteCallCount() == 1;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestTemplateMethodPattern - 重複執行防護",
                noRepeatedExecution,
                "重複執行防護正常"
            ));
        }

        delete container;
    }

    // 測試流水線管理
    void TestPipelineManagement()
    {
        Print("--- 測試 TradingPipelineContainerBase 流水線管理 ---");

        TestTradingPipelineContainerImpl* container = new TestTradingPipelineContainerImpl("ManageTest", "", "Test", false, 3);

        // 創建測試流水線
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        // 測試添加流水線
        bool added1 = container.AddPipeline(pipeline1);
        bool added2 = container.AddPipeline(pipeline2);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestPipelineManagement - 添加流水線",
                added1 && added2 && container.GetPipelineCount() == 2,
                "流水線添加成功"
            ));
        }

        // 測試流水線查詢
        bool hasPipeline1 = container.HasPipeline(pipeline1);
        bool hasPipeline2 = container.HasPipeline(pipeline2);
        ITradingPipeline* retrieved = container.GetPipeline(0);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestPipelineManagement - 流水線查詢",
                hasPipeline1 && hasPipeline2 && retrieved == pipeline1,
                "流水線查詢正常"
            ));
        }

        // 測試移除流水線
        bool removed = container.RemovePipeline(pipeline1);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestPipelineManagement - 移除流水線",
                removed && container.GetPipelineCount() == 1 && !container.HasPipeline(pipeline1),
                "流水線移除成功"
            ));
        }

        delete container;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試執行流程
    void TestExecuteFlow()
    {
        Print("--- 測試 TradingPipelineContainerBase 執行流程 ---");

        TestTradingPipelineContainerImpl* container = new TestTradingPipelineContainerImpl("ExecuteTest");

        // 添加測試流水線
        MockTradingPipeline* pipeline = new MockTradingPipeline("TestPipeline");
        container.AddPipeline(pipeline);

        // 執行容器
        container.Execute();

        // 檢查執行狀態
        bool containerExecuted = container.IsExecuted();
        bool pipelineExecuted = pipeline.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestExecuteFlow - 執行狀態",
                containerExecuted && pipelineExecuted,
                "執行流程正常"
            ));
        }

        // 測試重置功能
        container.Restore();
        bool containerRestored = !container.IsExecuted();
        bool pipelineRestored = !pipeline.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineContainerBase::TestExecuteFlow - 重置功能",
                containerRestored && pipelineRestored,
                "重置功能正常"
            ));
        }

        delete container;
        delete pipeline;
    }
};

//+------------------------------------------------------------------+
//| 測試實現類（用於測試抽象基類）                                   |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerImpl : public TradingPipelineContainerBase
{
private:
    int m_preExecuteCallCount;
    int m_executeInternalCallCount;
    int m_postExecuteCallCount;

public:
    TestTradingPipelineContainerImpl(string name, string description = "", string type = "TestImpl", bool owned = false, int maxPipelines = 50)
        : TradingPipelineContainerBase(name, description, type, owned, maxPipelines),
          m_preExecuteCallCount(0), m_executeInternalCallCount(0), m_postExecuteCallCount(0) {}

    virtual ~TestTradingPipelineContainerImpl() {}

    // 公共方法用於測試受保護的 SetResult
    void TestSetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        SetResult(success, message, errorLevel);
    }

    // 測試模板方法調用
    void TestTemplateMethodCalls()
    {
        ResetCallCounts();
        Execute();
    }

    // 重置調用計數
    void ResetCallCounts()
    {
        m_preExecuteCallCount = 0;
        m_executeInternalCallCount = 0;
        m_postExecuteCallCount = 0;
    }

    // 獲取調用計數
    int GetPreExecuteCallCount() { return m_preExecuteCallCount; }
    int GetExecuteInternalCallCount() { return m_executeInternalCallCount; }
    int GetPostExecuteCallCount() { return m_postExecuteCallCount; }

protected:
    // 實現抽象方法
    virtual bool PreExecuteCheck() override
    {
        m_preExecuteCallCount++;
        return true;
    }

    virtual void ExecuteInternal() override
    {
        m_executeInternalCallCount++;
        // 執行所有子流水線
        for(int i = 0; i < m_pipelines.Size(); i++)
        {
            ITradingPipeline* pipeline = m_pipelines.Get(i);
            if(pipeline != NULL && !pipeline.IsExecuted())
            {
                pipeline.Execute();
            }
        }
    }

    virtual void PostExecuteProcess() override
    {
        m_postExecuteCallCount++;
    }
};
